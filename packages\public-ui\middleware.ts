import { type NextRequest, NextResponse } from 'next/server';
import { server } from './config';

export const restrictedPage = '/restricted-region';
export default async function middleware(req: NextRequest) {
  /**temp code start */
  function isValidAuthHeader(authHeader: string): boolean {
    const base64Credentials = authHeader.split(' ')[1];
    const credentials = Buffer.from(base64Credentials, 'base64').toString(
      'ascii',
    );
    const [username, password] = credentials.split(':');

    const validUsername = '88762b65-3d52';
    const validPassword = '4002-861a-425b4ee60b16';

    return username === validUsername && password === validPassword;
  }

  const authHeader = req.headers.get('Authorization');
  if (!authHeader || !isValidAuthHeader(authHeader)) {
    return new NextResponse('Unauthorized', {
      status: 401,
      headers: { 'WWW-Authenticate': 'Basic realm="Secure Area"' },
    });
  }

  /**  temp code end */

  // Inorder to enable Geo Blocking restrictions, uncomment the following code

  // const imagePattern = /\.(jpg|jpeg|png|gif|webp|svg|ico)$/i; // Add any other image extensions as needed
  // const videoPattern = /\.(mp4|mov|avi|mkv|wmv)$/i; // Add any other video extensions as needed
  // const blacklistedRegions = ['US', 'KP', 'CU', 'IR', 'SY'];

  // if (
  //   imagePattern.test(req.nextUrl.pathname) ||
  //   videoPattern.test(req.nextUrl.pathname) ||
  //   req.nextUrl.pathname === restrictedPage
  // ) {
  //   return NextResponse.next();
  // }

  // const response = await fetch(`${server}/auth/client-details`, {
  //   method: 'GET',
  //   headers: {
  //     'Content-Type': 'application/json',
  //     'x-airlyft-ip':
  //       req.headers.get('x-forwarded-for') ||
  //       req.headers.get('x-real-ip') ||
  //       '',
  //   },
  // });
  // const responseJson = await response.json();
  // if (blacklistedRegions.includes(responseJson?.countryCode)) {
  //   const url = new URL(restrictedPage, req.url);
  //   url.searchParams.set('country', responseJson?.countryCode);
  //   return NextResponse.redirect(url);
  // }
  if (req.nextUrl.pathname.endsWith('/default-quests')) {
    const projectPath = req.nextUrl.pathname.replace('/default-quests', '');
    const url = new URL(projectPath, req.url);
    url.search = req.nextUrl.search;
    return NextResponse.redirect(url);
  }
  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico|404).*)'],
};
